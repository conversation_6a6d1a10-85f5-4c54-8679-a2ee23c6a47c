package com.cdkit.modules.cm.application.procurement;

import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisEntity;
import com.cdkit.modules.cm.domain.procurement.repository.CostProcurementExecutionBasisRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 采购执行依据应用服务
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProcurementExecutionBasisApplication {

    private final CostProcurementExecutionBasisRepository costProcurementExecutionBasisRepository;

    /**
     * 分页查询采购执行依据列表
     * 按照创建时间倒序排列
     * 
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<CostProcurementExecutionBasisEntity> queryPageList(CostProcurementExecutionBasisEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序排列
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return costProcurementExecutionBasisRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 根据ID查询采购执行依据详情（包含明细）
     * 
     * @param id 采购执行依据ID
     * @return 采购执行依据实体
     */
    public CostProcurementExecutionBasisEntity getById(String id) {
        return costProcurementExecutionBasisRepository.getById(id);
    }

    /**
     * 获取导出数据列表
     * 
     * @param queryEntity 查询条件
     * @return 导出数据列表
     */
    public List<CostProcurementExecutionBasisEntity> getExportList(CostProcurementExecutionBasisEntity queryEntity) {
        try {
            // 查询所有符合条件的数据
            PageReq pageReq = new PageReq();
            pageReq.setCurrent(1L);
            pageReq.setSize(10000L); // 设置一个较大的数值来获取所有数据

            // 按照创建时间倒序排列
            OrderParam createTimeParam = new OrderParam();
            createTimeParam.setField("create_time");
            createTimeParam.setOrder("desc");
            pageReq.setOrderParam(Arrays.asList(createTimeParam));

            PageRes<CostProcurementExecutionBasisEntity> pageRes = costProcurementExecutionBasisRepository.queryPageList(queryEntity, pageReq);

            // 处理空结果的情况
            if (pageRes == null || pageRes.getRecords() == null) {
                log.info("查询结果为空，返回空列表");
                return java.util.Collections.emptyList();
            }

            log.info("获取导出数据成功，共{}条记录", pageRes.getRecords().size());
            return pageRes.getRecords();

        } catch (Exception e) {
            log.error("获取导出数据失败", e);
            throw new RuntimeException("获取导出数据失败: " + e.getMessage());
        }
    }

    /**
     * 保存采购执行依据
     * 
     * @param entity 采购执行依据实体
     * @return 保存后的实体
     */
    public CostProcurementExecutionBasisEntity save(CostProcurementExecutionBasisEntity entity) {
        return costProcurementExecutionBasisRepository.save(entity);
    }

    /**
     * 更新采购执行依据
     * 
     * @param entity 采购执行依据实体
     * @return 更新后的实体
     */
    public CostProcurementExecutionBasisEntity update(CostProcurementExecutionBasisEntity entity) {
        return costProcurementExecutionBasisRepository.update(entity);
    }

    /**
     * 根据ID删除采购执行依据
     * 
     * @param id 采购执行依据ID
     */
    public void deleteById(String id) {
        costProcurementExecutionBasisRepository.deleteById(id);
    }

    /**
     * 批量删除采购执行依据
     * 
     * @param ids 采购执行依据ID列表
     */
    public void deleteBatch(List<String> ids) {
        costProcurementExecutionBasisRepository.deleteBatch(ids);
    }
}
