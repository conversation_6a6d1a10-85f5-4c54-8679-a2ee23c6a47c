package com.cdkit.modules.cm.performance.procurement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.procurement.dto.CostProcurementExecutionBasisDTO;
import com.cdkit.modules.cm.api.procurement.dto.CostProcurementExecutionBasisQueryDTO;
import com.cdkit.modules.cm.application.procurement.ProcurementExecutionBasisApplication;
import com.cdkit.modules.cm.application.procurement.converter.CostProcurementExecutionBasisConverter;
import com.cdkit.modules.cm.domain.procurement.entity.CostProcurementExecutionBasisEntity;
import com.cdkit.common.page.PageRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 采购执行依据控制器
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Tag(name = "采购执行依据管理", description = "采购执行依据相关接口")
@RestController
@RequestMapping("/cost/procurement/execution-basis")
@RequiredArgsConstructor
@Slf4j
public class CostProcurementExecutionBasisController {

    private final ProcurementExecutionBasisApplication procurementExecutionBasisApplication;

    /**
     * 分页查询采购执行依据列表
     * 支持材料名称、材料编码模糊查询，执行日期查询
     * 按照创建时间倒序排列，使用子母表表现形式
     * 
     * @param materialName 材料名称（支持模糊查询）
     * @param materialCode 材料编码（支持模糊查询）
     * @param executionDate 执行日期（具体到日期）
     * @param quarter 所在季度
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    @Operation(summary = "采购执行依据-分页列表查询", description = "支持材料名称、材料编码模糊查询，执行日期查询，按创建时间倒序排列")
    @GetMapping("/list")
    public Result<IPage<CostProcurementExecutionBasisDTO>> queryPageList(
            @Parameter(description = "材料名称") @RequestParam(required = false) String materialName,
            @Parameter(description = "材料编码") @RequestParam(required = false) String materialCode,
            @Parameter(description = "执行日期") @RequestParam(required = false) String executionDate,
            @Parameter(description = "所在季度") @RequestParam(required = false) String quarter,
            @Parameter(description = "页码") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @Parameter(description = "每页数量") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        try {
            // 构建查询条件
            CostProcurementExecutionBasisQueryDTO queryDTO = new CostProcurementExecutionBasisQueryDTO();
            queryDTO.setMaterialName(materialName);
            queryDTO.setMaterialCode(materialCode);
            queryDTO.setQuarter(quarter);

            // 处理executionDate字符串转Date
            if (StringUtils.hasText(executionDate)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date date = sdf.parse(executionDate);
                    queryDTO.setExecutionDate(date);
                } catch (Exception e) {
                    log.warn("执行日期格式错误: {}", executionDate, e);
                }
            }

            // 转换为领域实体
            CostProcurementExecutionBasisEntity queryEntity = CostProcurementExecutionBasisConverter.toEntity(queryDTO);

            // 执行查询
            PageRes<CostProcurementExecutionBasisEntity> pageRes = procurementExecutionBasisApplication.queryPageList(queryEntity, pageNo, pageSize);

            // 构建返回结果
            IPage<CostProcurementExecutionBasisDTO> page = new Page<>(pageNo, pageSize);
            if (pageRes != null) {
                page.setCurrent(pageRes.getCurrent());
                page.setSize(pageRes.getSize());
                page.setTotal(pageRes.getTotal());
                page.setRecords(CostProcurementExecutionBasisConverter.toDTOList(pageRes.getRecords()));
            }

            return Result.OK(page);

        } catch (Exception e) {
            log.error("查询采购执行依据列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询采购执行依据详情
     * 
     * @param id 采购执行依据ID
     * @return 采购执行依据详情
     */
    @Operation(summary = "采购执行依据-详情查询", description = "根据ID查询采购执行依据详情，包含明细信息")
    @GetMapping("/{id}")
    public Result<CostProcurementExecutionBasisDTO> getById(@PathVariable String id) {
        try {
            CostProcurementExecutionBasisEntity entity = procurementExecutionBasisApplication.getById(id);
            if (entity == null) {
                return Result.error("采购执行依据不存在");
            }
            
            CostProcurementExecutionBasisDTO dto = CostProcurementExecutionBasisConverter.toDTO(entity);
            return Result.OK(dto);
            
        } catch (Exception e) {
            log.error("查询采购执行依据详情失败，ID: {}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 导出采购执行依据数据
     * 
     * @param materialName 材料名称（支持模糊查询）
     * @param materialCode 材料编码（支持模糊查询）
     * @param executionDate 执行日期
     * @param quarter 所在季度
     * @return 导出数据列表
     */
    @Operation(summary = "采购执行依据-导出", description = "导出符合条件的采购执行依据数据")
    @GetMapping("/export")
    public Result<List<CostProcurementExecutionBasisDTO>> exportData(
            @Parameter(description = "材料名称") @RequestParam(required = false) String materialName,
            @Parameter(description = "材料编码") @RequestParam(required = false) String materialCode,
            @Parameter(description = "执行日期") @RequestParam(required = false) String executionDate,
            @Parameter(description = "所在季度") @RequestParam(required = false) String quarter) {

        try {
            // 构建查询条件
            CostProcurementExecutionBasisQueryDTO queryDTO = new CostProcurementExecutionBasisQueryDTO();
            queryDTO.setMaterialName(materialName);
            queryDTO.setMaterialCode(materialCode);
            queryDTO.setQuarter(quarter);

            // 处理executionDate字符串转Date
            if (StringUtils.hasText(executionDate)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date date = sdf.parse(executionDate);
                    queryDTO.setExecutionDate(date);
                } catch (Exception e) {
                    log.warn("执行日期格式错误: {}", executionDate, e);
                }
            }

            // 转换为领域实体
            CostProcurementExecutionBasisEntity queryEntity = CostProcurementExecutionBasisConverter.toEntity(queryDTO);

            // 获取导出数据
            List<CostProcurementExecutionBasisEntity> exportList = procurementExecutionBasisApplication.getExportList(queryEntity);

            // 转换为DTO
            List<CostProcurementExecutionBasisDTO> dtoList = CostProcurementExecutionBasisConverter.toDTOList(exportList);

            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("导出采购执行依据数据失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }


}
